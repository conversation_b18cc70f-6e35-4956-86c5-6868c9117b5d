/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { afterEach, describe, expect, it, vi } from "vitest"

import { ImageGenerationService } from "./core"
import { PicsumProvider } from "./providers"

describe("ImageGenerationService", () => {
  const picsumProvider = new PicsumProvider()
  const service = new ImageGenerationService([picsumProvider])

  // Restore mocks and spies after each test to ensure clean state
  afterEach(() => {
    vi.restoreAllMocks()
  })

  it.skip("should list available providers", () => {
    const providers = service.listProviders()

    expect(providers).toEqual([
      {
        id: "picsum",
        fullName: "Picsum Photos",
        shortName: "Picsum",
        supportsOutpainting: true,
      },
    ])
  })

  it.skip(
    "should generate valid image instances when provided with a valid request",
    async () => {
      const generatedImage = await service.generate("picsum", {
        prompt: "ignored prompt",
        aspectRatio: "wide",
      })

      expect(generatedImage.image).toBeInstanceOf(Buffer)
      expect(generatedImage.image.length).toBeGreaterThan(0)
      expect(generatedImage.mimeType).toBe("image/jpeg")
      expect(generatedImage.dimensions).toEqual({ width: 1792, height: 1024 })
    },
    { timeout: 10000 }
  )

  it.skip("should throw an error when requesting to generate images using an unknown provider", async () => {
    await expect(() =>
      // TODO: Check with Simon how to test this using (vitest) type assertions
      // @ts-expect-error We know that the provider ID is invalid, so we expect an error
      service.generate("unknown", "ignored prompt", {
        aspectRatio: "wide",
      })
    ).rejects.toThrow("Provider with ID 'unknown' not found")
  })

  it.skip("should raise error when provider fails to generate an image", async () => {
    // Mock the generateImage method to force a failure
    vi.spyOn(picsumProvider, "generate").mockRejectedValue(new Error("Generation failed"))

    await expect(
      service.generate("picsum", {
        prompt: "ignored prompt",
        aspectRatio: "wide",
      })
    ).rejects.toThrow()
  })

  it.skip("should raise error when download fails", async () => {
    // Mock fetch to simulate a failed download
    vi.spyOn(global, "fetch").mockResolvedValue({ ok: false } as Response)

    await expect(
      service.generate("picsum", {
        prompt: "ignored prompt",
        aspectRatio: "wide",
      })
    ).rejects.toThrow()
  })

  it.skip("should be able to successfully outpaint (i.e. extend) images", { timeout: 10000 }, async () => {
    // Given
    const baseImage = await service.generate("picsum", {
      prompt: "ignored prompt",
      aspectRatio: "wide",
    })

    // When
    const outpaintedImage = await service.outpaint("picsum", {
      prompt: "Ignored prompt",
      baseImage,
      left: 0.5,
      right: 0.5,
      up: 0.5,
      down: 0.5,
    })

    // Then
    expect(outpaintedImage.image).toBeInstanceOf(Buffer)
    expect(outpaintedImage.image.length).toBeGreaterThan(0)
    expect(outpaintedImage.mimeType).toBe("image/jpeg")
    expect(outpaintedImage.dimensions).toEqual({ width: 3584, height: 2048 })
  })
})
