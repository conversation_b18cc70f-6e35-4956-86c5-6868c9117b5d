/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
"use client"

import type { RouterOutput } from "@/lib/trpc-provider"
import type { FC } from "react"
import { useState } from "react"
import { ReportedGHGEmissions } from "@/app/admin/companies/[id]/components/ghg-emissions-table"
import { statusStyles } from "@/app/admin/companies/[id]/components/status-styles"
import { DetailDisplay } from "@/components/survey-form/detail-display"
// import { DetailDisplay } from "@/components/survey-form/detail-display"
import { Language } from "@/utils/constants"
import { Newspaper } from "lucide-react"
import { useLocale, useTranslations } from "next-intl"
import { CartesianGrid, Line, LineChart, XAxis, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

import type { ChartConfig } from "@kreios/ui/chart"
import { cn } from "@kreios/ui"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@kreios/ui/card"
import { ChartContainer, ChartLegend, ChartLegendContent, ChartTooltip, ChartTooltipContent } from "@kreios/ui/chart"
import { Link } from "@kreios/ui/link"

import { ScoringSource } from "../types"

type Company = RouterOutput["companies"]["get"]

// New components
const SummaryCard: FC<{ company: Company }> = ({ company }) => {
  // Assuming the evaluation percentage is calculated from the latestEvaluation

  const t = useTranslations("companyProfile.companyOverview.summary")

  const currentLocale = useLocale() as Language
  const [isAboutExpanded, setIsAboutExpanded] = useState(false)
  const toggleReadMore = () => setIsAboutExpanded(!isAboutExpanded)

  const about = (currentLocale === Language.EN ? company.aboutEn : company.aboutEt) ?? company.about ?? ""
  const shouldTruncate = about.length > 820

  return (
    <Card className="flex flex-col">
      <CardHeader className="flex flex-row justify-between gap-2 pb-4">
        <div className="flex-1">
          <div className="flex flex-col gap-2">
            <h3 className="font-semibold">{t("companyActivities")}</h3>
            <ul className="space-y-2">
              {company.businessActivities.map((activity) => (
                <li key={activity.id} className="flex items-start">
                  <span className="text-sm">
                    {currentLocale === Language.EN ? activity.naceCode.label : activity.naceCode.labelEt}(
                    {activity.naceCode.code})
                  </span>
                </li>
              ))}
            </ul>
          </div>
        </div>
        {/* <div className="sm:flex-shrink-0">
          <ChartContainer config={chartConfig} className="aspect-square h-[115px]">
            <RadialBarChart
              data={evaluationData}
              startAngle={startAngle}
              endAngle={endAngle}
              innerRadius={48}
              outerRadius={94}
            >
              <PolarGrid
                gridType="circle"
                radialLines={false}
                stroke="none"
                className="first:fill-muted last:fill-background"
                polarRadius={[52, 42]}
              />
              <RadialBar dataKey="value" background fill="hsl(var(--chart-1))" cornerRadius={4} />
              <PolarRadiusAxis tick={false} tickLine={false} axisLine={false}>
                <Label
                  content={({ viewBox }) => {
                    if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                      return (
                        <text x={viewBox.cx} y={viewBox.cy} textAnchor="middle" dominantBaseline="middle">
                          <tspan x={viewBox.cx} y={viewBox.cy} className="fill-foreground text-2xl font-bold">
                            {totalEvaluated}%
                          </tspan>
                          <tspan x={viewBox.cx} y={(viewBox.cy ?? 0) + 24} className="fill-muted-foreground text-xs">
                            {t("evaluated")}
                          </tspan>
                        </text>
                      )
                    }
                  }}
                />
              </PolarRadiusAxis>
            </RadialBarChart>
          </ChartContainer>
        </div> */}
      </CardHeader>
      <CardContent className="mb-4 max-h-[250px] flex-1 overflow-y-auto">
        {!about ? (
          <div className="flex h-full items-center justify-center">
            <caption className="text-center text-sm text-muted-foreground">{t("noData")}</caption>
          </div>
        ) : (
          <div className="text-sm">
            {shouldTruncate && !isAboutExpanded ? `${about.substring(0, 820)} ...` : about}{" "}
            {shouldTruncate && (
              <span
                className="cursor-pointer text-sm text-blue-600 hover:underline dark:text-blue-400"
                onClick={toggleReadMore}
              >
                {isAboutExpanded ? t("readLess") : t("readMore")}
              </span>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

interface ESGData {
  name: string
  management_score_status: keyof typeof statusStyles
  transparency_score_status: keyof typeof statusStyles
}

interface ESGStatusPillsProps {
  data: ESGData
}

const StatusPills: FC<ESGStatusPillsProps> = ({ data }) => {
  const t = useTranslations("common.scoringStatus")
  const tabsLabel = useTranslations("selfAssessmentForm")

  return (
    <div className="flex max-h-[50px] flex-1 flex-col gap-2 text-sm">
      <div className="flex flex-row items-center gap-4">
        <div className="font-base w-1/2">{tabsLabel(`${data.name}.title`)}</div>
        <div className="flex w-1/2 flex-row gap-4">
          <div className="w-1/2 text-center">
            <div className={cn("max-w-[120px] rounded-lg px-2 py-1", statusStyles[data.management_score_status])}>
              {t(data.management_score_status)}
            </div>
          </div>
          <div className="w-1/2 text-center">
            <div className={cn("max-w-[120px] rounded-lg px-2 py-1", statusStyles[data.transparency_score_status])}>
              {t(data.transparency_score_status)}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

interface ESGCardProps {
  company: Company
  setTab: (tab: string) => void
}

export const ESGCard: FC<ESGCardProps> = ({ company, setTab }) => {
  const t = useTranslations("companyProfile.companyOverview.esgRisks")
  const tBasis = useTranslations("companyProfile.scoringBasis")

  const categories = Object.entries(company.companyScoring.data)
  const userSource = company.companyScoring.source_type === ScoringSource.USER
  const basisLabel = (
    <div className="flex items-center text-base font-semibold">
      <span>{tBasis("basis")}: </span>
      <span className="ml-1 text-primary">{userSource ? tBasis("selfAssessment") : tBasis("publicData")}</span>
    </div>
  )

  const basisDescription = userSource ? tBasis("basisAssessmentDescription") : tBasis("basisPublicDescription")

  return (
    <Card className="flex flex-col">
      <CardHeader>
        <CardTitle className="flex flex-row items-center justify-between gap-4">
          <div>{t("title")}</div>
          <DetailDisplay
            label={basisLabel}
            helpInfo={{
              title: basisLabel,
              description: basisDescription,
            }}
            showInPopup
          />
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-1 flex-col gap-6">
        {categories.length === 0 && <caption className="m-auto text-sm text-muted-foreground">{t("noRisks")}</caption>}
        <div className="flex flex-1 flex-col gap-4 lg:max-h-[280px]">
          {categories.map(([key, category], index) => {
            const categoryData: ESGData = {
              name: key,
              management_score_status: category.management_score_status as keyof typeof statusStyles,
              transparency_score_status: category.transparency_score_status as keyof typeof statusStyles,
            }
            return <StatusPills key={index} data={categoryData} />
          })}
        </div>
        <button
          className="ml-auto mt-auto text-sm font-medium text-blue-600 hover:underline dark:text-blue-400"
          onClick={() => setTab("esg-assessment")}
        >
          {t("assessmentDetails")}
        </button>
      </CardContent>
    </Card>
  )
}
const AboutCompanyCard: FC<{ company: Company }> = ({ company }) => {
  const t = useTranslations("companyProfile.companyOverview.about")
  // const lastEvaluation = company.publishedEvaluations.at(-1)

  return (
    <Card>
      {/* <CardHeader>
        <CardTitle>{t("title")}</CardTitle>
      </CardHeader> */}
      <CardContent className="p-8">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          <div>
            <h3 className="text-base font-semibold">{t("country")}</h3>
            <p className="text-sm">{company.country || "N/A"}</p>
          </div>
          <div>
            <h3 className="text-base font-semibold">{t("employees")}</h3>
            <p className="text-sm">{company.employeeCount ?? "N/A"}</p>
          </div>
          <div>
            <h3 className="text-base font-semibold">{t("website")}</h3>
            {company.website ? (
              <a
                href={company.website}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-blue-600 hover:underline dark:text-blue-400"
              >
                {company.website}
              </a>
            ) : (
              <span className="text-sm">N/A</span>
            )}
          </div>
          <div>
            <h3 className="text-base font-semibold">{t("address")}</h3>
            <p className="text-sm">{`${company.address.street} ${company.address.houseNumber}, ${company.address.postalCode} ${company.address.city}`}</p>
          </div>
          <div>
            <h3 className="text-base font-semibold">{t("registrationNumber")}</h3>
            <p className="text-sm">{company.registrationNumber ?? "N/A"}</p>
          </div>
          {!!company.registrationDate && (
            <div>
              <h3 className="text-base font-semibold">{t("registrationDate")}</h3>
              <p className="text-sm">{new Date(company.registrationDate).toLocaleDateString()}</p>
            </div>
          )}
          {/* {!!lastEvaluation?.completedAt && (
            <div>
              <h3 className="text-lg font-semibold">{t("lastAssessmentDate")}</h3>
              <p className="text-sm">{new Date(lastEvaluation.completedAt).toLocaleDateString()}</p>
            </div>
          )} */}
        </div>
      </CardContent>
    </Card>
  )
}

const getColorFromSentiment = (sentiment: undefined | string) => {
  switch (sentiment) {
    case "Positive":
      return "text-green-500 dark:text-green-400"
    case "Negative":
      return "text-red-500 dark:text-red-400"
    default:
      return "text-gray-500 dark:text-gray-400"
  }
}

const SourcesCard: FC<{ company: Company }> = ({ company }) => {
  const t = useTranslations("companyProfile.companyOverview.sources")

  if (!company.mediaSources) return null

  return (
    <Card className="flex flex-col">
      <CardHeader>
        <CardTitle>{t("title")}</CardTitle>
      </CardHeader>
      <CardContent className="flex-1">
        {company.mediaSources.length > 0 ? (
          <div className="space-y-4">
            <h3 className="font-semibold">
              <div className="text-blue-600 dark:text-blue-400">{t("mediaCoverage")}</div>
            </h3>
            <ul className="space-y-2">
              {company.mediaSources
                .filter(({ title }) => title.length > 3)
                .map((mediaSource) => (
                  <li key={mediaSource.id} className="flex items-start">
                    <Newspaper className={cn("mr-2 h-5 w-5", getColorFromSentiment(mediaSource.sentiment))} />
                    <Link href={mediaSource.url} target="_blank" rel="noopener noreferrer">
                      {mediaSource.title}
                    </Link>
                  </li>
                ))}
            </ul>
          </div>
        ) : (
          <div className="flex h-full items-center justify-center">
            <caption className="text-center text-sm text-muted-foreground">{t("noData")}</caption>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

const FinancialsCard: FC<{ company: Company }> = ({ company }) => {
  const t = useTranslations("companyProfile.companyOverview.financials")
  const sortedAnnualReports = company.annualReports.slice().sort((a, b) => a.financialYear - b.financialYear)
  const financialData = sortedAnnualReports.map((report) => ({
    year: report.financialYear,
    revenue: report.revenue?.amount,
    profit: report.netProfit?.amount,
    assets: report.totalAssets?.amount,
  }))
  const currency = sortedAnnualReports.at(0)?.revenue?.currency ?? "EUR"

  const financialChartConfig = {
    revenue: {
      label: t("revenue"),
      color: "hsl(var(--chart-1))",
    },
    profit: {
      label: t("profit"),
      color: "hsl(var(--chart-2))",
    },
    assets: {
      label: t("assets"),
      color: "hsl(var(--chart-3))",
    },
  } satisfies ChartConfig

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("title")}</CardTitle>
        <CardDescription>
          {sortedAnnualReports.at(0)?.financialYear} - {sortedAnnualReports.at(-1)?.financialYear}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={financialChartConfig}>
          <LineChart
            accessibilityLayer
            data={financialData}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis dataKey="year" tickLine={false} axisLine={false} tickMargin={8} />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickCount={3}
              tickFormatter={(value: number) =>
                new Intl.NumberFormat("en-US", {
                  style: "currency",
                  currency: currency,

                  compactDisplay: "short",
                  notation: "compact",
                }).format(value)
              }
            />
            <ChartTooltip
              formatter={(value: number) =>
                new Intl.NumberFormat("en-US", {
                  style: "currency",
                  currency: currency,
                  maximumFractionDigits: 0,
                }).format(value)
              }
              cursor={false}
              content={<ChartTooltipContent />}
            />
            <Line dataKey="revenue" type="monotone" stroke="var(--color-revenue)" strokeWidth={2} dot={false} />
            <Line dataKey="profit" type="monotone" stroke="var(--color-profit)" strokeWidth={2} dot={false} />
            <Line dataKey="assets" type="monotone" stroke="var(--color-assets)" strokeWidth={2} dot={false} />
            <ChartLegend content={<ChartLegendContent className="flex-wrap gap-2 gap-x-4 text-base" />} />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

export const CompanyOverview: FC<{ company: Company; setTab: (tab: string) => void }> = ({ company, setTab }) => {
  return (
    <div className="flex flex-col gap-4">
      <div className="grid gap-6 lg:grid-cols-2">
        <SummaryCard company={company} />
        <ESGCard company={company} setTab={setTab} />
      </div>
      <AboutCompanyCard company={company} />
      <div className="grid grid-cols-1">
        <ReportedGHGEmissions company={company} />
      </div>
      <div className="grid gap-6 md:grid-cols-2">
        <SourcesCard company={company} />
        <FinancialsCard company={company} />
      </div>
    </div>
  )
}
