/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { FC, ReactNode } from "react"
import Link from "next/link"
import { requestAccess } from "@/actions/request-access"
import { ArrowLeft, CheckCircle, Loader2Icon, MailIcon } from "lucide-react"
import { useFormState, useFormStatus } from "react-dom"

import { cn } from "@kreios/ui"
import { Button } from "@kreios/ui/button"
import { Input } from "@kreios/ui/input"
import { Label } from "@kreios/ui/label"

interface RequestAccessFormProps {
  branding?: ReactNode
  userEmail?: string
  content: {
    title: string
    description: string
    emailLabel: string
    companyName: string
    cancel: string
    submit: string
    successTitle: string
    successMessage: string
    backToLogin: string
  }
}

type RequestAccessState = {
  success?: boolean
  error?: {
    type: "VALIDATION_ERROR" | "UNKNOWN_ERROR"
    message: string
    fieldErrors?: Record<string, string[]>
  }
}

const ErrorMessage: FC<{ children?: ReactNode; className?: string }> = ({ children, className }) => {
  if (!children) return null

  return <p className={cn("text-sm font-medium text-destructive", className)}>{children}</p>
}

const SubmitButton: FC<{ children: ReactNode }> = ({ children }) => {
  const { pending } = useFormStatus()

  return (
    <Button
      type="submit"
      variant="outline"
      disabled={pending}
      className="mt-2 inline-flex h-9 items-center justify-center whitespace-nowrap rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
    >
      {pending ? <Loader2Icon className="mr-2 h-4 w-4 animate-spin" /> : <MailIcon className="mr-2 h-4 w-4" />}
      {children}
    </Button>
  )
}

const SuccessView: FC<{ content: { successTitle: string; successMessage: string; backToLogin: string } }> = ({
  content,
}) => (
  <div className="space-y-4 text-center">
    <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
      <CheckCircle className="h-8 w-8 text-green-600" />
    </div>
    <div className="space-y-2">
      <h2 className="text-2xl font-bold text-gray-900">{content.successTitle}</h2>
      <p className="text-gray-600">{content.successMessage}</p>
    </div>
    <Button asChild variant="outline" className="w-full">
      <Link href="/auth/login">
        <ArrowLeft className="mr-2 h-4 w-4" />
        {content.backToLogin}
      </Link>
    </Button>
  </div>
)

const FormContent: FC<{
  userEmail?: string
  content: RequestAccessFormProps["content"]
  state: RequestAccessState
}> = ({ userEmail, content, state }) => {
  const { pending } = useFormStatus()

  return (
    <>
      <ErrorMessage className="text-center">{state.error?.message}</ErrorMessage>

      <div className="grid gap-2">
        <div className="grid gap-2">
          <Label
            htmlFor="email"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {content.emailLabel}
          </Label>
          <Input
            id="email"
            name="email"
            type="email"
            value={userEmail ?? ""}
            required
            disabled={pending}
            className={cn(
              "flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
              state.error?.fieldErrors?.email ? "border-destructive" : ""
            )}
            autoCapitalize="none"
            autoComplete="email"
            autoCorrect="off"
          />
          {state.error?.fieldErrors?.email && <ErrorMessage>{state.error.fieldErrors.email[0]}</ErrorMessage>}
        </div>

        <div className="mt-4 grid gap-2">
          <Label
            htmlFor="companyName"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {content.companyName}
          </Label>
          <Input
            id="companyName"
            name="companyName"
            required
            disabled={pending}
            className={cn(
              "flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
              state.error?.fieldErrors?.companyName ? "border-destructive" : ""
            )}
          />
          {state.error?.fieldErrors?.companyName && (
            <ErrorMessage>{state.error.fieldErrors.companyName[0]}</ErrorMessage>
          )}
        </div>

        <SubmitButton>{content.submit}</SubmitButton>
      </div>

      <div className="text-center">
        <Button asChild variant="ghost" className="w-full" disabled={pending}>
          <Link href="/auth/login">
            <ArrowLeft className="mr-2 h-4 w-4" />
            {content.cancel}
          </Link>
        </Button>
      </div>
    </>
  )
}

export const RequestAccessForm: FC<RequestAccessFormProps> = ({ branding, userEmail, content }) => {
  const [state, formAction] = useFormState(requestAccess, {})

  if (state.success) {
    return (
      <div className="mx-auto grid w-[22.5rem] gap-6">
        <div className="mb-10 flex items-center justify-center text-primary lg:hidden">{branding}</div>
        <SuccessView
          content={{
            successTitle: content.successTitle,
            successMessage: content.successMessage,
            backToLogin: content.backToLogin,
          }}
        />
      </div>
    )
  }

  return (
    <div className="mx-auto grid w-[22.5rem] gap-6">
      <div className="mb-10 flex items-center justify-center text-primary lg:hidden">{branding}</div>

      <div className="grid gap-2 text-center">
        <h1 className="text-3xl font-bold">{content.title}</h1>
        <p className="text-balance text-muted-foreground">{content.description}</p>
      </div>

      <form action={formAction} className="grid gap-4">
        <FormContent userEmail={userEmail} content={content} state={state} />
      </form>
    </div>
  )
}
