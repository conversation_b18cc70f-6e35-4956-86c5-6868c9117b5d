/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { FC } from "react"
import { useRef, useState } from "react"
import { HelpIcon } from "@/components/survey-form/help-icon"
import { useHelpInformation } from "@/components/survey-form/use-help-information-context"
import { FileText, X } from "lucide-react"
import { useTranslations } from "next-intl"

import { cn } from "@kreios/ui"
import { Button } from "@kreios/ui/button"
import { Card, CardContent, CardFooter, CardHeader } from "@kreios/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@kreios/ui/dialog"

type DetailDisplayProps = {
  label: React.ReactNode
  className?: string
  helpInfo?: {
    title: React.ReactNode
    description: string
    exampleDocumentUrl?: string
  }
  required?: boolean
  showInPopup?: boolean
}

export const DetailDisplay: FC<DetailDisplayProps> = ({
  label,
  helpInfo,
  required,
  className,
  showInPopup = false,
}) => {
  const { helpInfo: helpDescription, setHelpInfo } = useHelpInformation()
  const t = useTranslations("survey.components.detailDisplay")
  const mobileHelpCardRef = useRef<HTMLDivElement>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const handleHelpClick = () => {
    if (!helpInfo) return
    if (showInPopup) {
      setIsModalOpen(true)
    }
    setHelpInfo(helpInfo)
  }

  return (
    <div className="flex flex-col">
      <div className="flex w-full items-center">
        <h5
          className={cn(
            "help-trigger inline-flex items-center gap-1 text-sm font-medium",
            helpInfo && "cursor-pointer hover:text-primary",
            className
          )}
          onClick={handleHelpClick}
        >
          <span className="inline-flex items-center">
            {label}
            {required && <span className="ml-1 text-destructive">*</span>}
          </span>
          {helpInfo && <HelpIcon handleHelpClick={handleHelpClick} />}
        </h5>
      </div>

      {helpDescription && (
        <>
          {showInPopup ? (
            <Dialog
              open={isModalOpen}
              onOpenChange={(open) => {
                setIsModalOpen(open)
                if (!open) setHelpInfo(null)
              }}
            >
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader className="gap-3">
                  <DialogTitle>{helpDescription.title}</DialogTitle>
                  <DialogDescription>{helpDescription.description}</DialogDescription>
                </DialogHeader>
              </DialogContent>
            </Dialog>
          ) : (
            <Card className="block h-fit pt-4 md:hidden" ref={mobileHelpCardRef}>
              <CardHeader className="flex flex-row items-baseline justify-between gap-2 space-y-0 pb-2">
                <div className="text-sm font-medium">{helpDescription.title}</div>
                <Button variant="ghost" size="icon" onClick={() => setHelpInfo(null)}>
                  <X className="h-4 w-4" />
                </Button>
              </CardHeader>
              <CardContent className="scrollbar-thin scrollbar-track-transparent scrollbar-thumb-gray-300 hover:scrollbar-thumb-gray-400 max-h-[150px] overflow-y-auto pr-2">
                <p className="text-sm text-muted-foreground">{helpDescription.description}</p>
              </CardContent>
              {helpDescription.exampleDocumentUrl && (
                <CardFooter>
                  <span className="flex items-center text-sm text-blue-600 transition-colors hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                    <FileText className="mr-2 h-4 w-4" />
                    {t("viewExampleDocument")}
                  </span>
                </CardFooter>
              )}
            </Card>
          )}
        </>
      )}
    </div>
  )
}
