/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ.. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ..
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { getTranslations } from "next-intl/server"

import {
  Body,
  Button,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from "@kreios/mail-sender/utils"

// Define styles for the email template
const main = {
  backgroundColor: "#ffffff",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
}

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  maxWidth: "580px",
}

const logoContainer = {
  display: "flex",
  alignItems: "center",
  marginBottom: "24px",
}

const logo = {
  marginRight: "12px",
}

const heading = {
  fontSize: "32px",
  fontWeight: "bold",
  color: "#18181B",
}

const paragraph = {
  fontSize: "16px",
  lineHeight: "26px",
}

const button = {
  backgroundColor: "#22c55e",
  borderRadius: "4px",
  color: "#fff",
  fontWeight: "bold",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  width: "100%",
  padding: "12px 20px",
  fontSize: "16px",
  boxShadow: "0 4px 6px rgba(34, 197, 94, 0.25)",
  transition: "all 0.2s ease-in-out",
}

const hr = {
  borderColor: "#cccccc",
  margin: "20px 0",
}

const footer = {
  color: "#8898aa",
  fontSize: "12px",
}

const infoBox = {
  backgroundColor: "#f8fafc",
  border: "1px solid #e2e8f0",
  borderRadius: "4px",
  padding: "16px",
  marginBottom: "20px",
}

const infoHeading = {
  fontSize: "18px",
  fontWeight: "bold",
  marginBottom: "8px",
  color: "#334155",
}

// Define the props for the email template
interface EnhancedSurveyInvitationEmailProps {
  companyName: string
  surveyTemplate: string
  surveyLink: string
  recipientName?: string
  logoUrl?: string
}

/**
 * Enhanced email template for survey invitations
 */
export const EnhancedSurveyInvitationEmail = async ({
  companyName,
  surveyTemplate,
  surveyLink,
  recipientName,
  logoUrl = "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-ELWb3Keeayd904WemgAFMeITHlfceC.png",
}: EnhancedSurveyInvitationEmailProps) => {
  // Get translations for the 'email' namespace
  const t = await getTranslations("emailContent.sendSurveyEmail")

  // Determine the type based on surveyTemplate
  const isSelfAssessment = surveyTemplate === "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1"
  const type = isSelfAssessment ? t("selfAssessment") : t("survey")

  // Format the template name for display
  const templateName =
    surveyTemplate === "UNIFIED_QUESTIONNAIRE_V1"
      ? "Unified Questionnaire"
      : surveyTemplate === "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1"
        ? "Self Assessment Unified Questionnaire"
        : surveyTemplate

  return (
    <Html>
      <Head />
      <Preview>{t("subject", { type, templateName, companyName })}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            {logoUrl && <Img src={logoUrl} width="80" height="80" alt="Impactly Logo" style={logo} />}
            <Heading style={heading}>Impactly</Heading>
          </Section>
          <Text style={paragraph}>{t("greeting", { name: recipientName ?? "" })}</Text>
          <Text style={paragraph}>
            {t("invitation", { type: type.toLowerCase() })}
            <span>
              <strong>{companyName}</strong>
            </span>
          </Text>

          <Section style={infoBox}>
            <Text style={infoHeading}>{t("details.label", { type })}</Text>
            <Text style={paragraph}>{t("details.type", { templateName })}</Text>
          </Section>

          <Text style={paragraph}>{t("action", { type: type.toLowerCase() })}</Text>
          <Button style={button} href={surveyLink}>
            {t("button", { type })}
          </Button>
          <Hr style={hr} />
          <Text style={footer}>
            {t.rich("footer", {
              Link: (chunks) => <span className="text-blue-600">{chunks}</span>,
            })}
          </Text>
        </Container>
      </Body>
    </Html>
  )
}
