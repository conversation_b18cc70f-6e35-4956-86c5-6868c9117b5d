{"name": "@impactly/email", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts", "./env": "./env.ts", "./*": "./src/*.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore --ignore-path ../../.prettierignore", "lint": "eslint", "test": "vitest run --passWithNoTests", "test:watch": "vitest", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"@impactly/domain": "workspace:*", "@kreios/mail-sender": "workspace:*", "@t3-oss/env-core": "0.11.1", "awilix": "12.0.3", "next-intl": "4.0.2", "zod": "3.23.8"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/react": "18.3.5", "eslint": "9.10.0", "prettier": "3.4.2", "typescript": "5.6.2", "vitest": "2.1.9"}, "peerDependencies": {"react": "18.3.1"}}